.youmind-editor-node-list-item-ui {
  display: list-item;
  margin: 4px 0;
}

/* 无序列表项样式 */
.youmind-editor-node-bullet-list-ui .youmind-editor-node-list-item-ui {
  list-style: none;
  position: relative;
  padding-left: 1.5em;

  &::before {
    content: "•";
    position: absolute;
    left: 0.35rem;
    top: 0.35rem;
    font-size: 0.8rem;
    line-height: 1;
  }

  /* 确保没有原生的列表标记 */
  &::marker {
    content: none;
  }
}

/* 有序列表项样式 - 分层级渲染 */
.youmind-editor-node-ordered-list-ui {
  /* 重置计数器 */
  counter-reset: level1 level2 level3;

  .youmind-editor-node-list-item-ui {
    list-style: none;
    position: relative;
    padding-left: 1.25em;

    /* 确保不会有原生的列表标记 */
    &::marker {
      content: none;
    }

    p {
      margin-left: 0.5rem;
      line-height: inherit;
      margin-top: 0;
      margin-bottom: 0;
    }
  }
}

/* 第一层：1, 2, 3, 4... */
.youmind-editor-node-ordered-list-ui > .youmind-editor-node-list-item-ui {
  counter-increment: level1;

  &::before {
    content: counter(level1) ". ";
    position: absolute;
    left: 0;
    top: 0;
    font-weight: normal;
    color: inherit;
    font-size: inherit;
    line-height: inherit;
  }
}

/* 第二层：a, b, c... aa, bb, cc... (CSS原生行为) */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui > .youmind-editor-node-list-item-ui {
  counter-increment: level2;

  &::before {
    content: counter(level2, lower-alpha) ". ";
    position: absolute;
    left: 0;
    top: 0;
    font-weight: normal;
    color: inherit;
    font-size: inherit;
    line-height: inherit;
  }
}

/* 第三层：i, ii, iii, iv... */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui > .youmind-editor-node-list-item-ui {
  counter-increment: level3;

  &::before {
    content: counter(level3, lower-roman) ". ";
    position: absolute;
    left: 0;
    top: 0;
    font-weight: normal;
    color: inherit;
    font-size: inherit;
    line-height: inherit;
  }
}

/* 第四层及以上：回到数字 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui > .youmind-editor-node-list-item-ui {
  counter-increment: level1;

  &::before {
    content: counter(level1) ". ";
    position: absolute;
    left: 0;
    top: 0;
    font-weight: normal;
    color: inherit;
    font-size: inherit;
    line-height: inherit;
  }
}

/* 嵌套列表的计数器重置 */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui {
  counter-reset: level2;
}

.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui {
  counter-reset: level3;
}

.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui {
  counter-reset: level1;
}
